{"all_child_tables": [{"name": "Root Cause Prevention Strategy", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/root_cause_prevention_strategy/root_cause_prevention_strategy.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Price Change Request Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/price_change_request_detail/price_change_request_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "EFD Z Report Invoice", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/efd_z_report_invoice/efd_z_report_invoice.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Company Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_company_detail/tz_insurance_company_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Station Members", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/station_members/station_members.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Delivery Exchange Item Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/delivery_exchange_item_details/delivery_exchange_item_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Employee Piecework Additional Salary", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_piecework_additional_salary/employee_piecework_additional_salary.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Piecework Payment Allocation", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/piecework_payment_allocation/piecework_payment_allocation.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "BOM Additional Costs", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/bom_additional_costs/bom_additional_costs.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Special Closing Balance Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/special_closing_balance_detail/special_closing_balance_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Salary Slip OT Component", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/salary_slip_ot_component/salary_slip_ot_component.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Vehicle Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_vehicle_detail/tz_insurance_vehicle_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "File Attachment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/file_attachment/file_attachment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bank Clearance Pro Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/bank_clearance_pro_detail/bank_clearance_pro_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Document Attachment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/document_attachment/document_attachment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inter Company Stock Transfer Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inter_company_stock_transfer_details/inter_company_stock_transfer_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Reconciliation Pro Invoice", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/payment_reconciliation_pro_invoice/payment_reconciliation_pro_invoice.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "SQL Process Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/sql_process_detail/sql_process_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Reconciliation Pro Payment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/payment_reconciliation_pro_payment/payment_reconciliation_pro_payment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Repack Template Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/repack_template_detail/repack_template_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inv ERR Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inv_err_detail/inv_err_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Policy Holder Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_policy_holder_detail/tz_insurance_policy_holder_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inter Company Material Request Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inter_company_material_request_details/inter_company_material_request_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Single Piecework Employees", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/single_piecework_employees/single_piecework_employees.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Parking <PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/parking_bill_items/parking_bill_items.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Work Order Consignment Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/work_order_consignment_detail/work_order_consignment_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Delivery Exchange Non Stock Item Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/delivery_exchange_non_stock_item_details/delivery_exchange_non_stock_item_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Employee Salary Component Limit", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_salary_component_limit/employee_salary_component_limit.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "CSF TZ Bank Charges Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/csf_tz_bank_charges_detail/csf_tz_bank_charges_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Employee OT Component", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_ot_component/employee_ot_component.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Consignment Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/vehicle_consignment_detail/vehicle_consignment_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TRA TAX Inv Item", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tra_tax_inv_item/tra_tax_inv_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reporting Currency Settings Rate", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/reporting_currency_settings_rate/reporting_currency_settings_rate.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Parking <PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/parking_bill_details/parking_bill_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Email Employee <PERSON><PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/email_employee_salary_slip/email_employee_salary_slip.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Possible Root Cause", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/possible_root_cause/possible_root_cause.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Order Tracking Container", "file": "apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/order_tracking_container/order_tracking_container.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bin List", "file": "apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/bin_list/bin_list.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tire Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tire_details/tire_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Engine Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/engine_details/engine_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Equipment Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/equipment_table/equipment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Trip Location Update", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_trip_location_update/vehicle_trip_location_update.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Power Train Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/power_train_checklist/power_train_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Air System Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/air_system_checklist/air_system_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Brake System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/brake_system_details/brake_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel Request Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_request_table/fuel_request_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_details/fuel_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Suspension Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/suspension_details/suspension_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electronics Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electronics_checklist/electronics_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Trip Steps Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/trip_steps_table/trip_steps_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "<PERSON><PERSON><PERSON> Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/brake_checklist/brake_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Engine Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/engine_checklist/engine_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel System Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_checklist/fuel_system_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Lighting Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist/lighting_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Lighting Checklist Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist_details/lighting_checklist_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Air System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/air_system_details/air_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tire Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tire_checklist/tire_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Suspension Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/suspension_checklist/suspension_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Steering Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/steering_checklist/steering_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Power Train Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/power_train_details/power_train_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Steering Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/steering_details/steering_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Documents", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_documents/vehicle_documents.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Transport Assignment", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/transport_assignment/transport_assignment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tires Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tires_checklist/tires_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electrical Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electrical_checklist/electrical_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fixed Expense Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fixed_expense_table/fixed_expense_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Route Steps Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/route_steps_table/route_steps_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Service", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_service/vehicle_service.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Subtrips Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/subtrips_table/subtrips_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electrical Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electrical_details/electrical_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_checklist/vehicle_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tires Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tires_details/tires_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electronics Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electronics_details/electronics_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Products of Interest", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/products_of_interest/products_of_interest.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Customer <PERSON><PERSON>", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_item/customer_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Plan", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/payment_plan/payment_plan.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Stanbic Payments Info", "file": "apps/csf_tz/csf_tz/stanbic/doctype/stanbic_payments_info/stanbic_payments_info.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Machine Strip Request Item", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/machine_strip_request_item/machine_strip_request_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Funds Details", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/requested_funds_details/requested_funds_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Funds Accounts Table", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/requested_funds_accounts_table/requested_funds_accounts_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Pre Delivery Inspection Reading", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/pre_delivery_inspection_reading/pre_delivery_inspection_reading.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reference Payment Table", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/reference_payment_table/reference_payment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Pre Delivery Inspection Details", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/pre_delivery_inspection_details/pre_delivery_inspection_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Issued Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/issued_items_table/issued_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Used Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/used_items_table/used_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/requested_items_table/requested_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Workshop Services Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/workshop_services_table/workshop_services_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bond Reference Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond_reference_table/bond_reference_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container Issue Detail", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_issue_detail/container_issue_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container Detail", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_detail/container_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Required Permit", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/required_permit/required_permit.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Expenses", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/expenses/expenses.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Border Procedure Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border_procedure_table/border_procedure_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Import Border Procedure Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/import_border_procedure_table/import_border_procedure_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reporting Status Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/reporting_status_table/reporting_status_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Cargo Details", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/cargo_details/cargo_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Border Processing Vehicle Details", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border_processing_vehicle_details/border_processing_vehicle_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container File Closing Information", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_file_closing_information/container_file_closing_information.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Packing List", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/packing_list/packing_list.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Permits Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/permits_table/permits_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Mandatory Attachment Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/mandatory_attachment_table/mandatory_attachment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bond History Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond_history_table/bond_history_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}], "problematic_tables": [{"name": "Root Cause Prevention Strategy", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/root_cause_prevention_strategy/root_cause_prevention_strategy.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Price Change Request Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/price_change_request_detail/price_change_request_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "EFD Z Report Invoice", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/efd_z_report_invoice/efd_z_report_invoice.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Company Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_company_detail/tz_insurance_company_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Delivery Exchange Item Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/delivery_exchange_item_details/delivery_exchange_item_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "BOM Additional Costs", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/bom_additional_costs/bom_additional_costs.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Special Closing Balance Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/special_closing_balance_detail/special_closing_balance_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Salary Slip OT Component", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/salary_slip_ot_component/salary_slip_ot_component.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Vehicle Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_vehicle_detail/tz_insurance_vehicle_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "File Attachment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/file_attachment/file_attachment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bank Clearance Pro Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/bank_clearance_pro_detail/bank_clearance_pro_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Document Attachment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/document_attachment/document_attachment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inter Company Stock Transfer Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inter_company_stock_transfer_details/inter_company_stock_transfer_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Reconciliation Pro Invoice", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/payment_reconciliation_pro_invoice/payment_reconciliation_pro_invoice.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "SQL Process Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/sql_process_detail/sql_process_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Reconciliation Pro Payment", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/payment_reconciliation_pro_payment/payment_reconciliation_pro_payment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Repack Template Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/repack_template_detail/repack_template_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inv ERR Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inv_err_detail/inv_err_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TZ Insurance Policy Holder Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tz_insurance_policy_holder_detail/tz_insurance_policy_holder_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Inter Company Material Request Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/inter_company_material_request_details/inter_company_material_request_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Parking <PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/parking_bill_items/parking_bill_items.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Work Order Consignment Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/work_order_consignment_detail/work_order_consignment_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Delivery Exchange Non Stock Item Details", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/delivery_exchange_non_stock_item_details/delivery_exchange_non_stock_item_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Employee Salary Component Limit", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_salary_component_limit/employee_salary_component_limit.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "CSF TZ Bank Charges Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/csf_tz_bank_charges_detail/csf_tz_bank_charges_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Employee OT Component", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_ot_component/employee_ot_component.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Consignment Detail", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/vehicle_consignment_detail/vehicle_consignment_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "TRA TAX Inv Item", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/tra_tax_inv_item/tra_tax_inv_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reporting Currency Settings Rate", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/reporting_currency_settings_rate/reporting_currency_settings_rate.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Parking <PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/parking_bill_details/parking_bill_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Possible Root Cause", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/possible_root_cause/possible_root_cause.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Order Tracking Container", "file": "apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/order_tracking_container/order_tracking_container.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bin List", "file": "apps/csf_tz/csf_tz/purchase_and_stock_management/doctype/bin_list/bin_list.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tire Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tire_details/tire_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Engine Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/engine_details/engine_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Equipment Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/equipment_table/equipment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Trip Location Update", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_trip_location_update/vehicle_trip_location_update.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Power Train Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/power_train_checklist/power_train_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Air System Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/air_system_checklist/air_system_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Brake System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/brake_system_details/brake_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel Request Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_request_table/fuel_request_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_details/fuel_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Suspension Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/suspension_details/suspension_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electronics Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electronics_checklist/electronics_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Trip Steps Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/trip_steps_table/trip_steps_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "<PERSON><PERSON><PERSON> Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/brake_checklist/brake_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Engine Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/engine_checklist/engine_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fuel System Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fuel_system_checklist/fuel_system_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Lighting Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist/lighting_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Lighting Checklist Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/lighting_checklist_details/lighting_checklist_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Air System Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/air_system_details/air_system_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tire Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tire_checklist/tire_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Suspension Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/suspension_checklist/suspension_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Steering Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/steering_checklist/steering_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Power Train Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/power_train_details/power_train_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Steering Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/steering_details/steering_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Documents", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_documents/vehicle_documents.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Transport Assignment", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/transport_assignment/transport_assignment.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tires Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tires_checklist/tires_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electrical Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electrical_checklist/electrical_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Fixed Expense Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/fixed_expense_table/fixed_expense_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Route Steps Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/route_steps_table/route_steps_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Service", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_service/vehicle_service.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Subtrips Table", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/subtrips_table/subtrips_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electrical Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electrical_details/electrical_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Vehicle Checklist", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/vehicle_checklist/vehicle_checklist.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Tires Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/tires_details/tires_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Electronics Details", "file": "apps/csf_tz/csf_tz/fleet_management/doctype/electronics_details/electronics_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Products of Interest", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/products_of_interest/products_of_interest.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Customer <PERSON><PERSON>", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/customer_item/customer_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Payment Plan", "file": "apps/csf_tz/csf_tz/sales_and_marketing/doctype/payment_plan/payment_plan.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Stanbic Payments Info", "file": "apps/csf_tz/csf_tz/stanbic/doctype/stanbic_payments_info/stanbic_payments_info.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Machine Strip Request Item", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/machine_strip_request_item/machine_strip_request_item.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Funds Details", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/requested_funds_details/requested_funds_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Funds Accounts Table", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/requested_funds_accounts_table/requested_funds_accounts_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Pre Delivery Inspection Reading", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/pre_delivery_inspection_reading/pre_delivery_inspection_reading.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reference Payment Table", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/reference_payment_table/reference_payment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Pre Delivery Inspection Details", "file": "apps/csf_tz/csf_tz/after_sales_services/doctype/pre_delivery_inspection_details/pre_delivery_inspection_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Issued Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/issued_items_table/issued_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Used Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/used_items_table/used_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Requested Items Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/requested_items_table/requested_items_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Workshop Services Table", "file": "apps/csf_tz/csf_tz/workshop/doctype/workshop_services_table/workshop_services_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bond Reference Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond_reference_table/bond_reference_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container Issue Detail", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_issue_detail/container_issue_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container Detail", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_detail/container_detail.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Required Permit", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/required_permit/required_permit.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Expenses", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/expenses/expenses.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Border Procedure Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border_procedure_table/border_procedure_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Import Border Procedure Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/import_border_procedure_table/import_border_procedure_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Reporting Status Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/reporting_status_table/reporting_status_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Cargo Details", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/cargo_details/cargo_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Border Processing Vehicle Details", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/border_processing_vehicle_details/border_processing_vehicle_details.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Container File Closing Information", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/container_file_closing_information/container_file_closing_information.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Packing List", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/packing_list/packing_list.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Permits Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/permits_table/permits_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Mandatory Attachment Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/mandatory_attachment_table/mandatory_attachment_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}, {"name": "Bond History Table", "file": "apps/csf_tz/csf_tz/clearing_and_forwarding/doctype/bond_history_table/bond_history_table.json", "has_parent": false, "has_parenttype": false, "has_parentfield": false, "missing_fields": ["parent", "parenttype", "parentfield"]}], "good_tables": [{"name": "Station Members", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/station_members/station_members.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Employee Piecework Additional Salary", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/employee_piecework_additional_salary/employee_piecework_additional_salary.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Piecework Payment Allocation", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/piecework_payment_allocation/piecework_payment_allocation.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Single Piecework Employees", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/single_piecework_employees/single_piecework_employees.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}, {"name": "Email Employee <PERSON><PERSON>", "file": "apps/csf_tz/csf_tz/csf_tz/doctype/email_employee_salary_slip/email_employee_salary_slip.json", "has_parent": true, "has_parenttype": true, "has_parentfield": true, "missing_fields": []}]}