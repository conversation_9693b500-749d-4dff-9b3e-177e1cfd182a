#!/usr/bin/env python3

import json
import os
import shutil
from datetime import datetime

def fix_all_child_tables():
    """Fix all problematic child tables by adding missing parent fields"""
    
    print("=== Fixing All CSF TZ Child Tables ===")
    
    # Load analysis results
    try:
        with open('child_tables_analysis.json', 'r') as f:
            analysis = json.load(f)
    except FileNotFoundError:
        print("Error: child_tables_analysis.json not found. Run analyze_child_tables.py first.")
        return
    
    problematic_tables = analysis['problematic_tables']
    
    print(f"Found {len(problematic_tables)} child tables to fix")
    
    # Create backup directory
    backup_dir = f"child_tables_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    fixed_count = 0
    error_count = 0
    
    for table in problematic_tables:
        try:
            file_path = table['file']
            table_name = table['name']
            missing_fields = table['missing_fields']
            
            print(f"\nFixing: {table_name}")
            print(f"  File: {file_path}")
            print(f"  Missing: {', '.join(missing_fields)}")
            
            # Create backup
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            
            # Load the JSON file
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Add missing parent fields to field_order
            field_order = data.get('field_order', [])
            fields = data.get('fields', [])
            
            # Parent fields to add (in order)
            parent_fields_to_add = []
            if 'parent' in missing_fields:
                parent_fields_to_add.append('parent')
            if 'parentfield' in missing_fields:
                parent_fields_to_add.append('parentfield')
            if 'parenttype' in missing_fields:
                parent_fields_to_add.append('parenttype')
            
            # Add to field_order at the beginning
            new_field_order = parent_fields_to_add + field_order
            data['field_order'] = new_field_order
            
            # Add field definitions
            new_fields = []
            
            # Add parent field definitions
            if 'parent' in missing_fields:
                new_fields.append({
                    "fieldname": "parent",
                    "fieldtype": "Data",
                    "hidden": 1
                })
            
            if 'parentfield' in missing_fields:
                new_fields.append({
                    "fieldname": "parentfield",
                    "fieldtype": "Data",
                    "hidden": 1
                })
            
            if 'parenttype' in missing_fields:
                new_fields.append({
                    "fieldname": "parenttype",
                    "fieldtype": "Data",
                    "hidden": 1
                })
            
            # Add existing fields
            new_fields.extend(fields)
            data['fields'] = new_fields
            
            # Write back to file
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"  ✅ Fixed: Added {len(parent_fields_to_add)} parent fields")
            fixed_count += 1
            
        except Exception as e:
            print(f"  ❌ Error fixing {table_name}: {e}")
            error_count += 1
    
    print(f"\n=== Fix Summary ===")
    print(f"✅ Successfully fixed: {fixed_count} child tables")
    print(f"❌ Errors: {error_count} child tables")
    print(f"📁 Backups saved to: {backup_dir}")
    
    if fixed_count > 0:
        print(f"\n🎉 Ready to run 'bench migrate' to apply changes!")
    
    return fixed_count, error_count

if __name__ == "__main__":
    fix_all_child_tables()
