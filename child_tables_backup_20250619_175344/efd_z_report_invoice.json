{"creation": "2019-08-19 14:41:20.363473", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["invoice_number", "invoice_date", "amt_excl_vat", "vat", "amt_ex__sr", "invoice_amount", "include"], "fields": [{"columns": 1, "fieldname": "invoice_number", "fieldtype": "Link", "in_list_view": 1, "label": "Invoice No.", "options": "Sales Invoice", "read_only": 1, "reqd": 1}, {"columns": 1, "fetch_from": "invoice_number.posting_date", "fieldname": "invoice_date", "fieldtype": "Date", "in_list_view": 1, "label": "Invoice Date", "read_only": 1, "reqd": 1}, {"columns": 2, "fieldname": "invoice_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Invoice Amt.", "options": "invoice_currency", "read_only": 1, "reqd": 1, "width": "200"}, {"columns": 1, "default": "0", "fieldname": "include", "fieldtype": "Check", "in_list_view": 1, "label": "Include"}, {"columns": 2, "fieldname": "amt_excl_vat", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amt. Excl. VAT", "read_only": 1, "reqd": 1, "width": "200"}, {"columns": 1, "fieldname": "vat", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "VAT", "read_only": 1, "reqd": 1, "width": "200"}, {"columns": 2, "fieldname": "amt_ex__sr", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amt. EX / SR", "read_only": 1, "reqd": 1, "width": "200"}], "istable": 1, "modified": "2020-03-05 20:43:33.193085", "modified_by": "Administrator", "module": "CSF TZ", "name": "EFD Z Report Invoice", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}