{"actions": [], "allow_rename": 1, "creation": "2022-11-09 14:24:29.175301", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "price_list", "old_price", "column_break_4", "item_name", "cost", "new_price", "valid_from", "valid_to"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "price_list", "fieldtype": "Link", "in_list_view": 1, "label": "Price List", "options": "Price List", "reqd": 1}, {"fieldname": "old_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Old Price", "read_only": 1, "options": "price_list_currency"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name"}, {"fieldname": "new_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "New Price", "options": "price_list_currency"}, {"fieldname": "cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost"}, {"columns": 1, "fieldname": "valid_from", "fieldtype": "Date", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"columns": 1, "fieldname": "valid_to", "fieldtype": "Date", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "fetch_from": "price_list.currency"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2023-12-18 16:12:00.110059", "modified_by": "Administrator", "module": "CSF TZ", "name": "Price Change Request Detail", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}