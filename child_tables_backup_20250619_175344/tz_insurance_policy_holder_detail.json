{"actions": [], "allow_rename": 1, "creation": "2022-05-11 10:19:41.462744", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["createddate", "updateddate", "<PERSON><PERSON>", "updatedby", "id", "policyholdertypeid", "policyholderfullname", "policyholderbirthdate", "policyholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "policyholderidentitytypeid", "genderid", "countryid", "countrycode", "column_break_14", "districtid", "districtname", "regionname", "locationstreet", "policyholderphone1", "policyholderphone2", "policyholderphone3", "policyholderfax", "postaladdress", "emailaddress", "companyid", "statusid", "systemid"], "fields": [{"fieldname": "createddate", "fieldtype": "Data", "label": "createdDate"}, {"fieldname": "updateddate", "fieldtype": "Data", "label": "updatedDate"}, {"fieldname": "<PERSON><PERSON>", "fieldtype": "Data", "label": "created<PERSON>y"}, {"fieldname": "updatedby", "fieldtype": "Data", "label": "updatedBy"}, {"fieldname": "id", "fieldtype": "Data", "label": "id"}, {"fieldname": "policyholdertypeid", "fieldtype": "Data", "label": "policyHolderTypeId"}, {"fieldname": "policyholderfullname", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "policyHolderFullName"}, {"fieldname": "policyholderbirthdate", "fieldtype": "Data", "label": "policyHolderBirthDate"}, {"fieldname": "policyholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldtype": "Data", "in_list_view": 1, "label": "policyHolderIdentityNumber"}, {"fieldname": "policyholderidentitytypeid", "fieldtype": "Data", "label": "policyHolderIdentityTypeId"}, {"fieldname": "genderid", "fieldtype": "Data", "label": "genderId"}, {"fieldname": "countryid", "fieldtype": "Data", "label": "countryId"}, {"fieldname": "countrycode", "fieldtype": "Data", "label": "countryCode"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "districtid", "fieldtype": "Data", "label": "districtId"}, {"fieldname": "districtname", "fieldtype": "Data", "in_list_view": 1, "label": "districtName"}, {"fieldname": "regionname", "fieldtype": "Data", "in_list_view": 1, "label": "regionName"}, {"fieldname": "locationstreet", "fieldtype": "Data", "label": "locationStreet"}, {"fieldname": "policyholderphone1", "fieldtype": "Data", "label": "policyHolderPhone1"}, {"fieldname": "policyholderphone2", "fieldtype": "Data", "label": "policyHolderPhone2"}, {"fieldname": "policyholderphone3", "fieldtype": "Data", "label": "policyHolderPhone3"}, {"fieldname": "policyholderfax", "fieldtype": "Data", "label": "policyHolderFax"}, {"fieldname": "postaladdress", "fieldtype": "Data", "label": "postalAddress"}, {"fieldname": "emailaddress", "fieldtype": "Data", "label": "emailAddress"}, {"fieldname": "companyid", "fieldtype": "Data", "label": "companyId"}, {"fieldname": "statusid", "fieldtype": "Data", "label": "statusId"}, {"fieldname": "systemid", "fieldtype": "Data", "label": "systemId"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-05-11 22:23:19.812728", "modified_by": "Administrator", "module": "CSF TZ", "name": "TZ Insurance Policy Holder Detail", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}