{"creation": "2019-02-18 23:25:41.743404", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["invoice_type", "invoice_number", "invoice_currency", "column_break_4", "invoice_amount", "invoice_exchange_rate", "invoice_gain_or_loss"], "fields": [{"fieldname": "invoice_type", "fieldtype": "Select", "in_list_view": 1, "label": "Invoice Type", "options": "Sales Invoice\nPurchase Invoice"}, {"description": "Should work like Party Type / Party Name", "fieldname": "invoice_number", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Invoice Number", "options": "invoice_type"}, {"fieldname": "invoice_currency", "fieldtype": "Link", "in_list_view": 1, "label": "Invoice Currency", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "invoice_amount", "fieldtype": "Float", "in_list_view": 1, "label": "Invoice Amount", "options": "invoice_currency"}, {"fieldname": "invoice_exchange_rate", "fieldtype": "Float", "in_list_view": 1, "label": "Invoice Exchange Rate"}, {"fieldname": "invoice_gain_or_loss", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Invoice Gain or Loss", "options": "invoice_curency"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}], "istable": 1, "modified": "2020-10-11 22:47:10.437743", "modified_by": "Administrator", "module": "CSF TZ", "name": "Inv ERR Detail", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}