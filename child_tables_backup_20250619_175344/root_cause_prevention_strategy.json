{"actions": [], "allow_rename": 1, "creation": "2022-05-28 13:35:44.821933", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["solution_to_be_implemented", "consideration", "specify_considerations", "is_solution_implemented", "cost_column_break", "estimated_cost", "incidental_findings", "specify_findings", "date_of_completion"], "fields": [{"fieldname": "solution_to_be_implemented", "fieldtype": "Data", "in_list_view": 1, "label": "Solution to be implemented", "length": 1000, "reqd": 1}, {"fieldname": "cost_column_break", "fieldtype": "Column Break"}, {"fieldname": "estimated_cost", "fieldtype": "Float", "in_list_view": 1, "label": "Estimated cost"}, {"fieldname": "consideration", "fieldtype": "Select", "label": "Are there special considerations?", "options": "\nNo\nYes"}, {"depends_on": "eval: doc.consideration == \"Yes\"", "fieldname": "specify_considerations", "fieldtype": "Small Text", "label": "Please specify", "mandatory_depends_on": "eval: doc.consideration == \"Yes\""}, {"fieldname": "incidental_findings", "fieldtype": "Select", "label": "Are there incidental findings to be reviewed for corrective action?", "options": "\nNo\nYes"}, {"depends_on": "eval: doc.incidental_findings == \"Yes\"", "fieldname": "specify_findings", "fieldtype": "Small Text", "label": "Please specify", "mandatory_depends_on": "eval: doc.incidental_findings == \"Yes\""}, {"depends_on": "eval: doc.is_solution_implemented == \"Yes\"", "fieldname": "date_of_completion", "fieldtype": "Date", "in_list_view": 1, "label": "Date of Implementation", "mandatory_depends_on": "eval: doc.is_solution_implemented == \"Yes\""}, {"fieldname": "is_solution_implemented", "fieldtype": "Select", "in_list_view": 1, "label": "Is the solution implemented", "options": "\nNo\nYes", "reqd": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-06-01 16:20:38.015263", "modified_by": "Administrator", "module": "CSF TZ", "name": "Root Cause Prevention Strategy", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}