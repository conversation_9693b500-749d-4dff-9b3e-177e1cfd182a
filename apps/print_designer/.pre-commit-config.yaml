exclude: 'node_modules|.git'
default_stages: [commit]
fail_fast: false


repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
        files: "print_designer.*"
        exclude: ".*json$|.*txt$|.*csv|.*md|.*svg"
      - id: check-yaml
      - id: no-commit-to-branch
        args: ['--branch', 'main']
      - id: check-merge-conflict
      - id: check-ast
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: debug-statements

  - repo: https://github.com/frappe/black
    rev: 951ccf4d5bb0d692b457a5ebc4215d755618eb68
    hooks:
      - id: black

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types_or: [javascript, vue]
        # Ignore any files that might contain jinja / bundles
        exclude: |
            (?x)^(
                print_designer/public/dist/.*|
                .*node_modules.*|
                .*boilerplate.*|
                .*min\.js|
                print_designer/www/website_script.js|
                print_designer/templates/includes/.*|
                print_designer/public/js/lib/.*
            )$


  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/PyCQA/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        additional_dependencies: ['flake8-bugbear',]