{
	"branches": ["main"],
	"plugins": [
		"@semantic-release/commit-analyzer", {
			"preset": "angular"
		},
		"@semantic-release/release-notes-generator",
		[
			"@semantic-release/exec", {
				"prepareCmd": 'sed -ir "s/[0-9]*\.[0-9]*\.[0-9]*/${nextRelease.version}/" print_designer/__init__.py'
			}
		],
		[
			"@semantic-release/git", {
				"assets": ["print_designer/__init__.py"],
				"message": "chore(release): Bumped to Version ${nextRelease.version}"
			}
		],
		"@semantic-release/github"
	]
}