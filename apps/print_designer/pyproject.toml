[project]
name = "print_designer"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>"}
]
description = "Frappe App to Design Print Formats using interactive UI."
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    "PyQRCode~=1.2.1",
    "pypng~=0.20220715.0",
    "python-barcode~=0.15.1",
    "websockets",
    "distro",
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.black]
line-length = 99

[tool.isort]
line_length = 99
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
indent = "\t"

[deploy.dependencies.apt]
packages = [
    "fonts-liberation",
    "libatk-bridge2.0-0",
    "libatk1.0-0",
    "libatspi2.0-0",
    "libgbm1",
    "libgtk-3-0",
    "libnspr4",
    "libnss3",
    "xdg-utils",
    "libvulkan1",
    "libxcomposite1",
    "libxdamage1",
    "libxfixes3",
    "libxkbcommon0",
    "libxrandr2",
]
# already installed
# "ca-certificates",
# "libasound2",
# "libc6",
# "libcairo2",
# "libcups2",
# "libdbus-1-3",
# "libexpat1",
# "libglib2.0-0",
# "libpango-1.0-0",
# "wget",
# "libudev1",
# "libx11-6",
# "libxcb1",
# "libxext6",
# "libcurl3-gnutls",