# This action:
#
# 1. Generates release notes using github API.
# 2. Strips unnecessary info like chore/style etc from notes.
# 3. Updates release info.

name: 'Release Notes'

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag of release like v1.0.0'
        required: true
        type: string
  release:
    types: [released]

permissions:
  contents: read

jobs:
  regen-notes:
    name: 'Regenerate release notes'
    runs-on: ubuntu-latest

    steps:
    - name: Update notes
      run: |
        NEW_NOTES=$(gh api --method POST -H "Accept: application/vnd.github+json"  /repos/frappe/print_designer/releases/generate-notes  -f tag_name=$RELEASE_TAG \
          | jq -r '.body' \
          | sed -E '/^\* (chore|ci|test|docs|style)/d' \
          | sed -E 's/by @mergify //'
        )
        RELEASE_ID=$(gh api -H "Accept: application/vnd.github+json" /repos/frappe/print_designer/releases/tags/$RELEASE_TAG | jq -r '.id')
        gh api --method PATCH -H "Accept: application/vnd.github+json" /repos/frappe/print_designer/releases/$RELEASE_ID -f body="$NEW_NOTES"

      env:
        GH_TOKEN: ${{ secrets.RELEASE_TOKEN }}
        RELEASE_TAG: ${{ github.event.inputs.tag_name || github.event.release.tag_name }}