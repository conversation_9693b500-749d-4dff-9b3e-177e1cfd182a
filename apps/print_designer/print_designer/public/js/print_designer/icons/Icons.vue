<template>
	<svg
		id="printIcons"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		style="display: none"
	>
		<symbol id="layerPanel" fill="none" viewBox="0 0 16 16">
			<g filter="url(#a)">
				<path
					fill="var(--icon-stroke)"
					fill-rule="evenodd"
					d="M8.347 2.123a.7.7 0 0 0-.694 0L4.252 4.066 1.056 5.892a.7.7 0 0 0 0 1.216l3.196 1.826 3.4 1.943a.7.7 0 0 0 .695 0l3.401-1.943 3.196-1.826a.7.7 0 0 0 0-1.216l-6.597-3.77ZM4.748 4.934 8 3.076 13.992 6.5l-2.74 1.566L8 9.924 4.748 8.066 2.008 6.5l2.74-1.566Zm-3.5 4.132a.5.5 0 0 0-.496.868l3.5 2 3.4 1.943a.7.7 0 0 0 .695 0l3.401-1.943 3.5-2a.5.5 0 0 0-.496-.868l-3.5 2L8 12.924l-3.252-1.858-3.5-2Z"
					class="Union"
					clip-rule="evenodd"
				/>
			</g>
			<defs>
				<filter
					id="a"
					width="24"
					height="24"
					x="-4"
					y="-4"
					class="a"
					color-interpolation-filters="sRGB"
					filterUnits="userSpaceOnUse"
				>
					<feFlood flood-opacity="0" result="BackgroundImageFix" />
					<feGaussianBlur in="BackgroundImageFix" stdDeviation="2" />
					<feComposite
						in2="SourceAlpha"
						operator="in"
						result="effect1_backgroundBlur_84_50838"
					/>
					<feBlend
						in="SourceGraphic"
						in2="effect1_backgroundBlur_84_50838"
						result="shape"
					/>
				</filter>
			</defs>
		</symbol>
		<symbol id="mouseTool" fill="none" viewBox="0 0 16 16">
			<g clip-path="url(#a)">
				<path
					fill="var(--icon-stroke)"
					fill-rule="evenodd"
					d="M.646.646a.5.5 0 0 0-.119.517l4.828 14a.5.5 0 0 0 .927.046L9.1 9.102l1.781-.823 4.328-1.996a.5.5 0 0 0-.047-.927l-14-4.828a.5.5 0 0 0-.517.12Zm7.478 8.185-2.23 4.83L1.808 1.807l11.854 4.088-3.198 1.475-1.632.754-2.167-2.167a.5.5 0 1 0-.707.707L8.124 8.83Z"
					clip-rule="evenodd"
				/>
			</g>
			<defs>
				<clipPath id="a">
					<path fill="#fff" d="M16 0H0v16h16z" />
				</clipPath>
			</defs>
		</symbol>
		<symbol id="textTool" fill="none" viewBox="0 0 16 16">
			<path
				fill="var(--icon-stroke)"
				fill-rule="evenodd"
				d="M5.6 1.5a.5.5 0 0 0 0 1 1.9 1.9 0 0 1 1.9 1.9v7.221A1.9 1.9 0 0 1 5.6 13.5a.5.5 0 0 0 0 1A2.9 2.9 0 0 0 8 13.228a2.9 2.9 0 0 0 2.4 1.272.5.5 0 1 0 0-1 1.9 1.9 0 0 1-1.9-1.9V4.379A1.9 1.9 0 0 1 10.4 2.5a.5.5 0 1 0 0-1A2.9 2.9 0 0 0 8 2.772 2.9 2.9 0 0 0 5.6 1.5Z"
				clip-rule="evenodd"
			/>
		</symbol>
		<symbol id="tableTool" fill="none" viewBox="0 0 16 16">
			<path
				fill="var(--icon-stroke)"
				fill-rule="evenodd"
				d="M4.5 2h-1A1.5 1.5 0 0 0 2 3.5v9A1.5 1.5 0 0 0 3.5 14h1V2ZM5 1H3.5A2.5 2.5 0 0 0 1 3.5v9A2.5 2.5 0 0 0 3.5 15h9a2.5 2.5 0 0 0 2.5-2.5v-9A2.5 2.5 0 0 0 12.5 1H5Zm.5 1v3H14V3.5A1.5 1.5 0 0 0 12.5 2h-7Zm0 4v3.5H14V6H5.5Zm0 4.5V14h7a1.5 1.5 0 0 0 1.5-1.5v-2H5.5Z"
				clip-rule="evenodd"
			/>
		</symbol>
		<symbol id="imageTool" fill="none" viewBox="0 0 16 16">
			<path
				fill="var(--icon-stroke)"
				fill-rule="evenodd"
				d="M12.5 2.5h-9A1.5 1.5 0 0 0 2 4v8a1.5 1.5 0 0 0 1.102 1.447.502.502 0 0 1 .053-.06l6.429-6.112a.9.9 0 0 1 1.1-.11L14 9.252V4a1.5 1.5 0 0 0-1.5-1.5Zm0 11H4.488l5.729-5.447 3.767 2.37.016.01V12a1.5 1.5 0 0 1-1.5 1.5Zm-9-12h9A2.5 2.5 0 0 1 15 4v8a2.5 2.5 0 0 1-2.5 2.5h-9A2.5 2.5 0 0 1 1 12V4a2.5 2.5 0 0 1 2.5-2.5ZM6 5.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM5.25 7.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5Z"
				clip-rule="evenodd"
			/>
		</symbol>
		<symbol id="rectangleTool" fill="none" viewBox="0 0 16 16">
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M12.5 2H3.5C2.67157 2 2 2.67157 2 3.5V12.5C2 13.3284 2.67157 14 3.5 14H12.5C13.3284 14 14 13.3284 14 12.5V3.5C14 2.67157 13.3284 2 12.5 2ZM3.5 1C2.11929 1 1 2.11929 1 3.5V12.5C1 13.8807 2.11929 15 3.5 15H12.5C13.8807 15 15 13.8807 15 12.5V3.5C15 2.11929 13.8807 1 12.5 1H3.5Z"
				fill="var(--icon-stroke)"
			/>
		</symbol>
		<symbol id="barcodeTool" fill="none" viewBox="0 0 16 16">
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M0.5 4C0.776142 4 1 4.18172 1 4.40588V12.2191C1 12.4433 0.776142 12.625 0.5 12.625C0.223858 12.625 0 12.4433 0 12.2191V4.40588C0 4.18172 0.223858 4 0.5 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M2.12354 4C2.39968 4 2.62354 4.18172 2.62354 4.40588V12.2191C2.62354 12.4433 2.39968 12.625 2.12354 12.625C1.84739 12.625 1.62354 12.4433 1.62354 12.2191V4.40588C1.62354 4.18172 1.84739 4 2.12354 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M5.25293 4C5.52907 4 5.75293 4.18172 5.75293 4.40588V12.2191C5.75293 12.4433 5.52907 12.625 5.25293 12.625C4.97679 12.625 4.75293 12.4433 4.75293 12.2191V4.40588C4.75293 4.18172 4.97679 4 5.25293 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M6.87646 4C7.15261 4 7.37646 4.18172 7.37646 4.40588V12.2191C7.37646 12.4433 7.15261 12.625 6.87646 12.625C6.60032 12.625 6.37646 12.4433 6.37646 12.2191V4.40588C6.37646 4.18172 6.60032 4 6.87646 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M8.5 4C8.77614 4 9 4.18172 9 4.40588V12.2191C9 12.4433 8.77614 12.625 8.5 12.625C8.22386 12.625 8 12.4433 8 12.2191V4.40588C8 4.18172 8.22386 4 8.5 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10.9351 4C11.2112 4 11.4351 4.18172 11.4351 4.40588V12.2191C11.4351 12.4433 11.2112 12.625 10.9351 12.625C10.6589 12.625 10.4351 12.4433 10.4351 12.2191V4.40588C10.4351 4.18172 10.6589 4 10.9351 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M12.5586 4C12.8347 4 13.0586 4.18172 13.0586 4.40588V12.2191C13.0586 12.4433 12.8347 12.625 12.5586 12.625C12.2825 12.625 12.0586 12.4433 12.0586 12.2191V4.40588C12.0586 4.18172 12.2825 4 12.5586 4Z"
				fill="var(--icon-stroke)"
			/>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M14.9941 4C15.2703 4 15.4941 4.18172 15.4941 4.40588V12.2191C15.4941 12.4433 15.2703 12.625 14.9941 12.625C14.718 12.625 14.4941 12.4433 14.4941 12.2191V4.40588C14.4941 4.18172 14.718 4 14.9941 4Z"
				fill="var(--icon-stroke)"
			/>
		</symbol>
		<symbol id="alignTop">
			<path
				d="M2.1,1H21.9m1,0a1,1,0,0,0-1-1H2.1a1,1,0,0,0,0,1.94H21.9A1,1,0,0,0,22.87,1Zm-3.09,14v-10A.81.81,0,0,0,19,4.1H14.88a.8.8,0,0,0-.8.81v10a.8.8,0,0,0,.8.8H19A.8.8,0,0,0,19.78,14.92Zm1,0v-10A1.76,1.76,0,0,0,19,3.13H14.86A1.76,1.76,0,0,0,13.1,4.89V14.94a1.76,1.76,0,0,0,1.76,1.76H19A1.76,1.76,0,0,0,20.75,14.94Zm-5.7-9.87h3.76v9.68H15.05ZM10.64,22.22V4.91a.8.8,0,0,0-.8-.81H5.75a.81.81,0,0,0-.81.81V22.22a.81.81,0,0,0,.81.81H9.84A.8.8,0,0,0,10.64,22.22Zm1,0V4.89A1.76,1.76,0,0,0,9.86,3.13H5.72A1.76,1.76,0,0,0,4,4.89V22.25A1.76,1.76,0,0,0,5.72,24H9.86A1.76,1.76,0,0,0,11.62,22.25ZM5.91,5.07H9.67v17H5.91Z"
			/>
		</symbol>
		<symbol id="alignBottom">
			<path
				d="M2.1,23H21.9m0-1H2.1A1,1,0,0,0,2.1,24H21.9a1,1,0,0,0,0-1.94ZM19,8.28H14.88a.8.8,0,0,0-.8.8v10a.8.8,0,0,0,.8.81H19a.81.81,0,0,0,.81-.81v-10A.8.8,0,0,0,19,8.28Zm0-1H14.86A1.76,1.76,0,0,0,13.1,9.06v10a1.76,1.76,0,0,0,1.76,1.76H19a1.76,1.76,0,0,0,1.75-1.76v-10A1.76,1.76,0,0,0,19,7.3Zm-3.95,2h3.76v9.68H15.05ZM9.84,1H5.75a.81.81,0,0,0-.81.81V19.09a.81.81,0,0,0,.81.81H9.84a.8.8,0,0,0,.8-.81V1.78A.8.8,0,0,0,9.84,1Zm0-1H5.72A1.76,1.76,0,0,0,4,1.75V19.11a1.76,1.76,0,0,0,1.75,1.76H9.86a1.76,1.76,0,0,0,1.76-1.76V1.75A1.76,1.76,0,0,0,9.86,0ZM5.91,1.94H9.67v17H5.91Z"
			/>
		</symbol>
		<symbol id="alignLeft">
			<path
				d="M.84,22V2m0-1a1,1,0,0,0-1,1V22a1,1,0,0,0,2,0V2A1,1,0,0,0,.84,1ZM15,4.13H4.82A.81.81,0,0,0,4,4.94V9.1a.8.8,0,0,0,.81.8H15a.8.8,0,0,0,.81-.8V4.94A.81.81,0,0,0,15,4.13Zm0-1H4.8A1.77,1.77,0,0,0,3,4.92V9.11A1.77,1.77,0,0,0,4.8,10.88H15a1.77,1.77,0,0,0,1.77-1.77V4.92A1.77,1.77,0,0,0,15,3.15ZM5,8.92V5.11h9.79V8.92Zm17.36,4.45H4.82a.81.81,0,0,0-.81.81v4.16a.8.8,0,0,0,.81.8H22.35a.8.8,0,0,0,.81-.8V14.18A.81.81,0,0,0,22.35,13.37Zm0-1H4.8A1.77,1.77,0,0,0,3,14.16v4.19A1.77,1.77,0,0,0,4.8,20.12H22.36a1.78,1.78,0,0,0,1.78-1.77V14.16A1.78,1.78,0,0,0,22.36,12.39ZM5,18.16V14.35H22.17v3.81Z"
			/>
		</symbol>
		<symbol id="alignRight">
			<path
				d="M23.16,2V22m-1-20V22a1,1,0,0,0,2,0V2a1,1,0,0,0-2,0Zm-13.94,3V9.1A.8.8,0,0,0,9,9.9H19.18A.8.8,0,0,0,20,9.1V4.94a.81.81,0,0,0-.81-.81H9A.81.81,0,0,0,8.23,4.94Zm-1,0V9.11A1.77,1.77,0,0,0,9,10.88H19.2A1.77,1.77,0,0,0,21,9.11V4.92A1.77,1.77,0,0,0,19.2,3.15H9A1.77,1.77,0,0,0,7.25,4.92Zm2,4V5.11H19V8.92ZM.84,14.18v4.16a.8.8,0,0,0,.81.8H19.18a.8.8,0,0,0,.81-.8V14.18a.81.81,0,0,0-.81-.81H1.65A.81.81,0,0,0,.84,14.18Zm-1,0v4.19a1.78,1.78,0,0,0,1.78,1.77H19.2A1.77,1.77,0,0,0,21,18.35V14.16a1.77,1.77,0,0,0-1.77-1.77H1.64A1.78,1.78,0,0,0-.14,14.16Zm2,4V14.35H19v3.81Z"
			/>
		</symbol>
		<symbol id="alignVerticalCenter">
			<path
				d="M2.1,12H21.9M24,12a1,1,0,0,0-1.07-1H1.07a1,1,0,1,0,0,1.94H22.93A1,1,0,0,0,24,12Zm-4.22,5V7a.8.8,0,0,0-.81-.8H14.88a.8.8,0,0,0-.8.8V17a.8.8,0,0,0,.8.8H19A.8.8,0,0,0,19.78,17Zm1,0V7A1.75,1.75,0,0,0,19,5.22H14.86A1.75,1.75,0,0,0,13.1,7V17a1.75,1.75,0,0,0,1.76,1.75H19A1.75,1.75,0,0,0,20.75,17Zm-5.7-9.87h3.76v9.68H15.05Zm-4.41,13.5V3.34a.8.8,0,0,0-.8-.8H5.75a.8.8,0,0,0-.81.8V20.66a.8.8,0,0,0,.81.8H9.84A.8.8,0,0,0,10.64,20.66Zm1,0V3.32A1.75,1.75,0,0,0,9.86,1.57H5.72A1.75,1.75,0,0,0,4,3.32V20.68a1.75,1.75,0,0,0,1.75,1.75H9.86A1.75,1.75,0,0,0,11.62,20.68ZM5.91,3.51H9.67v17H5.91Z"
			/>
		</symbol>
		<symbol id="alignHorizontalCenter">
			<path
				d="M12,22.93V1.07M12,0a1,1,0,0,0-1,1.07V22.93a1,1,0,1,0,1.94,0V1.07A1,1,0,0,0,12,0Zm5,4.22H7a.8.8,0,0,0-.8.81V9.12a.8.8,0,0,0,.8.8H17a.8.8,0,0,0,.8-.8V5A.8.8,0,0,0,17,4.22Zm0-1H7A1.75,1.75,0,0,0,5.22,5V9.14A1.75,1.75,0,0,0,7,10.9H17a1.75,1.75,0,0,0,1.75-1.76V5A1.75,1.75,0,0,0,17,3.25ZM7.16,9V5.19h9.68V9Zm13.5,4.41H3.34a.8.8,0,0,0-.8.8v4.09a.8.8,0,0,0,.8.81H20.66a.8.8,0,0,0,.8-.81V14.16A.8.8,0,0,0,20.66,13.36Zm0-1H3.32a1.75,1.75,0,0,0-1.75,1.76v4.14A1.75,1.75,0,0,0,3.32,20H20.68a1.75,1.75,0,0,0,1.75-1.75V14.14A1.75,1.75,0,0,0,20.68,12.38ZM3.51,18.09V14.33h17v3.76Z"
			/>
		</symbol>
		<symbol id="textAlignLeft">
			<path
				d="M22,18.54H2.05a.94.94,0,1,1,0-1.87H22a.94.94,0,1,1,0,1.87ZM15,12a1,1,0,0,0-1-.93H2.05a.94.94,0,1,0,0,1.86H14A1,1,0,0,0,15,12Zm0,11.21a1,1,0,0,0-1-.93H2.05a.94.94,0,1,0,0,1.86H14A1,1,0,0,0,15,23.21ZM23,6.39A1,1,0,0,0,22,5.46H2.05a1,1,0,0,0-1,.93,1,1,0,0,0,1,.94H22A1,1,0,0,0,23,6.39ZM15,.79a1,1,0,0,0-1-.93H2.05A1,1,0,0,0,1,.79a1,1,0,0,0,1,.93H14A1,1,0,0,0,15,.79Z"
			/>
		</symbol>
		<symbol id="textAlignCenter">
			<path
				d="M22,18.42H2.05a.93.93,0,1,1,0-1.84H22a.93.93,0,1,1,0,1.84ZM19,12A1,1,0,0,0,18,11.08H6A1,1,0,0,0,5,12a1,1,0,0,0,1,.92H18A1,1,0,0,0,19,12Zm0,11A1,1,0,0,0,18,22.08H6a.93.93,0,1,0,0,1.84H18A1,1,0,0,0,19,23ZM23,6.5A1,1,0,0,0,22,5.58H2.05A1,1,0,0,0,1,6.5a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,6.5ZM19,1A1,1,0,0,0,18,.08H6A1,1,0,0,0,5,1a1,1,0,0,0,1,.92H18A1,1,0,0,0,19,1Z"
			/>
		</symbol>
		<symbol id="textAlignRight">
			<path
				d="M22,18.42H2.05a.93.93,0,1,1,0-1.84H22a.93.93,0,1,1,0,1.84ZM23,12A1,1,0,0,0,22,11.08H10A1,1,0,0,0,9,12a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,12Zm0,11A1,1,0,0,0,22,22.08H10a.93.93,0,1,0,0,1.84H22A1,1,0,0,0,23,23ZM23,6.5A1,1,0,0,0,22,5.58H2.05A1,1,0,0,0,1,6.5a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,6.5ZM23,1A1,1,0,0,0,22,.08H10A1,1,0,0,0,9,1a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,1Z"
			/>
		</symbol>
		<symbol id="textAlignJustify">
			<path
				d="M22,1.92H2.05A1,1,0,0,1,1,1a1,1,0,0,1,1-.92H22A1,1,0,0,1,23,1,1,1,0,0,1,22,1.92ZM23,6.5A1,1,0,0,0,22,5.58H2.05A1,1,0,0,0,1,6.5a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,6.5ZM23,12A1,1,0,0,0,22,11.08H2.05A1,1,0,0,0,1,12a1,1,0,0,0,1,.92H22A1,1,0,0,0,23,12Zm0,5.5A1,1,0,0,0,22,16.58H2.05a.93.93,0,1,0,0,1.84H22A1,1,0,0,0,23,17.5ZM23,23A1,1,0,0,0,22,22.08H2.05a.93.93,0,1,0,0,1.84H22A1,1,0,0,0,23,23Z"
			/>
		</symbol>
		<symbol id="fontSize">
			<path
				d="M.2,5.53V2H17.05V5.53H10.82V21.78H6.43V5.53Zm12.74,7.08V10.07H23.8v2.54H20.05V22H16.68V12.61Z"
			/>
		</symbol>
		<symbol id="lineHeight">
			<path
				d="M23.24,4.73H9.92a.76.76,0,1,1,0-1.52H23.24a.76.76,0,0,1,0,1.52ZM24,12a.76.76,0,0,0-.76-.75H9.92a.76.76,0,1,0,0,1.51H23.24A.76.76,0,0,0,24,12Zm0,7.61a.76.76,0,0,0-.76-.76H9.92a.76.76,0,1,0,0,1.52H23.24A.76.76,0,0,0,24,19.6ZM3.17,2.82.3,6a.28.28,0,0,0,.21.46H6.24A.28.28,0,0,0,6.45,6L3.58,2.82A.28.28,0,0,0,3.17,2.82ZM6.68,6.38a.75.75,0,0,0-.13-.83L4,2.66a.81.81,0,0,0-1.15,0L.2,5.55a.75.75,0,0,0-.13.83.76.76,0,0,0,.71.46H6A.75.75,0,0,0,6.68,6.38ZM3.39,3.17,6,6.08l-5.21,0,2.6-2.89-.28-.25Zm.19,18L6.45,18a.28.28,0,0,0-.21-.46H.51A.28.28,0,0,0,.3,18l2.87,3.18A.28.28,0,0,0,3.58,21.17Zm.37.16,2.6-2.89A.77.77,0,0,0,6,17.15H.78a.74.74,0,0,0-.71.46.75.75,0,0,0,.13.83l2.6,2.89a.79.79,0,0,0,1.15,0Zm2-3.4-2.6,2.89.28.25-.31-.25L.78,17.91Zm-2-13H2.73V17.15H4Zm.38-.38H2.35v13H4.4Zm-1.29.76h.53V16.77H3.11Z"
			/>
		</symbol>
		<symbol id="borderRadius">
			<path
				d="M2.05,23.6a2,2,0,0,1-2-2V8.16A7.77,7.77,0,0,1,7.72.4H22a2,2,0,1,1,0,4.09H7.74A3.68,3.68,0,0,0,4.1,8.16v13.4A2,2,0,0,1,2.05,23.6Z"
			/>
		</symbol>
		<symbol id="fontItalic">
			<path
				d="M16.48,3.45,12.09,20.52h4.45L15.62,24h-13l.92-3.48h4.1L12.06,3.45H7.8L8.64,0H21.41l-.92,3.45Z"
			/>
		</symbol>
		<symbol id="fontUnderLine">
			<path
				d="M16.23,0h3.42V12.31a6.71,6.71,0,0,1-.95,3.57A6.49,6.49,0,0,1,16,18.27a8.79,8.79,0,0,1-4,.86,8.83,8.83,0,0,1-4-.86A6.4,6.4,0,0,1,5.3,15.88a6.71,6.71,0,0,1-1-3.57V0H7.77V12a4.19,4.19,0,0,0,.52,2.1,3.68,3.68,0,0,0,1.46,1.44,4.7,4.7,0,0,0,2.25.52,4.7,4.7,0,0,0,2.25-.52,3.64,3.64,0,0,0,1.47-1.44,4.29,4.29,0,0,0,.51-2.1ZM2.49,24V21h19v3Z"
			/>
		</symbol>
		<symbol id="letterSpacing">
			<path
				d="M5.68,11.11H9.94l.94,2.79h2.2l-4-11.34H6.54l-4,11.34H4.74ZM7.77,4.89h.08L9.39,9.46H6.23Zm7.71,8.89a3.8,3.8,0,0,0,2.61.11,2.63,2.63,0,0,0,.82-.49,2.43,2.43,0,0,0,.51-.67h.07V13.9h1.93V8.21a2.88,2.88,0,0,0-.31-1.41,2.35,2.35,0,0,0-.81-.9,3.91,3.91,0,0,0-1.11-.47A5.73,5.73,0,0,0,18,5.29a4.91,4.91,0,0,0-1.62.26,3.3,3.3,0,0,0-1.25.79,3,3,0,0,0-.73,1.3l1.87.26a1.61,1.61,0,0,1,.58-.76A1.92,1.92,0,0,1,18,6.82,1.54,1.54,0,0,1,19,7.16a1.26,1.26,0,0,1,.37,1v0a.46.46,0,0,1-.21.42,1.72,1.72,0,0,1-.68.2l-1.21.14a11.55,11.55,0,0,0-1.2.21,3.86,3.86,0,0,0-1,.43,2.1,2.1,0,0,0-.72.76,2.36,2.36,0,0,0-.27,1.19,2.5,2.5,0,0,0,.38,1.4A2.32,2.32,0,0,0,15.48,13.78Zm.74-2.93a1.37,1.37,0,0,1,.56-.39,4.25,4.25,0,0,1,.8-.2l.47-.06.55-.09a3.06,3.06,0,0,0,.5-.12.88.88,0,0,0,.32-.14v1a1.65,1.65,0,0,1-.24.87,1.71,1.71,0,0,1-.69.64,2.13,2.13,0,0,1-1,.24,1.74,1.74,0,0,1-1-.28,1,1,0,0,1-.41-.84A.92.92,0,0,1,16.22,10.85Zm7.7,7.84-2.67,2.67a.26.26,0,0,1-.45-.19V19.41H3.2v1.74a.28.28,0,0,1-.47.19L.08,18.69a.26.26,0,0,1,0-.38l2.65-2.66a.28.28,0,0,1,.47.2v1.74H20.8V15.83a.26.26,0,0,1,.45-.19l2.67,2.67A.26.26,0,0,1,23.92,18.69Z"
			/>
		</symbol>
		<symbol id="borderWidth">
			<path d="M23.75,5.75H.25V4.25h23.5Zm0,4H.25v2.5h23.5Zm0,6.5H.25v3.5h23.5Z" />
		</symbol>
		<symbol id="borderAll">
			<rect
				id="background"
				x="2"
				y="2"
				width="20"
				height="20"
				style="fill: var(--gray-100)"
			/>
			<path
				d="M23,0H1A1,1,0,0,0,0,1V23a1,1,0,0,0,1,1H23a1,1,0,0,0,1-1V1A1,1,0,0,0,23,0ZM22,22H2V2H22Z"
			/>
		</symbol>
		<symbol id="borderLeftStyle">
			<rect
				id="background"
				x="2"
				y="2"
				width="20"
				height="20"
				style="fill: var(--gray-100)"
			/>
			<path
				id="mainBorder"
				d="M24,23V1a1,1,0,0,0-1-1H2V2H22V22H2v2H23A1,1,0,0,0,24,23Z"
				style="fill: var(--gray-300)"
			/>
			<path id="activeBorder" d="M2,2V0H1A1,1,0,0,0,0,1V23a1,1,0,0,0,1,1H2V2Z" />
		</symbol>
		<symbol id="borderRightStyle">
			<rect
				id="background"
				x="2"
				y="2"
				width="20"
				height="20"
				style="fill: var(--gray-100)"
			/>
			<path
				id="mainBorder"
				d="M0,1V23a1,1,0,0,0,1,1H22V22H2V2H22V0H1A1,1,0,0,0,0,1Z"
				style="fill: var(--gray-300)"
			/>
			<path id="activeBorder" d="M22,22v2h1a1,1,0,0,0,1-1V1a1,1,0,0,0-1-1H22V22Z" />
		</symbol>
		<symbol id="borderTopStyle">
			<rect
				id="background"
				x="2"
				y="2"
				width="20"
				height="20"
				style="fill: var(--gray-100)"
			/>
			<path
				id="mainBorder"
				d="M1,24H23a1,1,0,0,0,1-1V2H22V22H2V2H0V23A1,1,0,0,0,1,24Z"
				style="fill: var(--gray-300)"
			/>
			<path id="activeBorder" d="M22,2h2V1a1,1,0,0,0-1-1H1A1,1,0,0,0,0,1V2H22Z" />
		</symbol>
		<symbol id="borderBottomStyle">
			<rect
				id="background"
				x="2"
				y="2"
				width="20"
				height="20"
				style="fill: var(--gray-100)"
			/>
			<path
				id="mainBorder"
				d="M23,0H1A1,1,0,0,0,0,1V22H2V2H22V22h2V1A1,1,0,0,0,23,0Z"
				style="fill: var(--gray-300)"
			/>
			<path id="activeBorder" d="M2,22H0v1a1,1,0,0,0,1,1H23a1,1,0,0,0,1-1V22H2Z" />
		</symbol>
	</svg>
</template>
