<!DOCTYPE html>
<html lang={{ lang }} dir={{ layout_direction }}>
	<head>
		<meta charset="utf-8">
		<link rel="preconnect" href="https://fonts.gstatic.com" />

		{% if html_id=="footer-html" %}

			{# link tag does not work in footer in wkhtmltopdf,
				so this is a workaround to include bootstrap and still have auto footer height working #}
			<style>
			{{ css }}
			</style>

		{% else %}

			{% for tag in head -%}
				{{ tag | string }}
			{%- endfor %}

		{% endif %}

		<style>
			body {
				margin: 0 !important;
				border: 0 !important;
				box-sizing: border-box;
				padding: 0mm !important;
			}
			/* Dont show explicit links for <a> tags */
			@media print {
				a[href]:after {
					content: none;
				}
			}
		</style>

		<!-- from: http://wkhtmltopdf.org/usage/wkhtmltopdf.txt -->
		<script>
			function subst() {
				var vars = {};
				var x = window.location.search.substring(1).split('&');
				for (var i in x) {
					var z = x[i].split('=',2);
					vars[z[0]] = unescape(z[1]);
				}
				var x = ['page','topage','time','date'];
				for (var i in x) {
					var y = document.getElementsByClassName("page_info_" + x[i]);
					for (var j=0; j<y.length; ++j) {
						y[j].textContent = vars[x[i]];
					}
				}

				const headers = {
					firstPage : document.getElementById("firstPageHeader"),
					oddPage : document.getElementById("oddPageHeader"),
					evenPage : document.getElementById("evenPageHeader"),
					lastPage : document.getElementById("lastPageHeader"),
				}
				const footers = {
					firstPage : document.getElementById("firstPageFooter"),
					oddPage : document.getElementById("oddPageFooter"),
					evenPage : document.getElementById("evenPageFooter"),
					lastPage : document.getElementById("lastPageFooter"),
				}
				function displayHeaderFooter(elements, selectedElements) {
					for (var key in elements) {
						if (elements[key]) {
							if (elements[key] === selectedElements) {
								elements[key].style.display = "block";
							} else {
								elements[key].style.display = "none";
							}
						}
					}
				}
				var page_no = parseInt(vars["page"]);
				var total_page_no = parseInt(vars["topage"]);
				if (page_no == 1) {
					if (headers.firstPage) {
						displayHeaderFooter(headers, headers.firstPage);
					} else {
						displayHeaderFooter(headers, headers.oddPage);
					}
					if (footers.firstPage) {
						displayHeaderFooter(footers, footers.firstPage);
					} else {
						displayHeaderFooter(footers, footers.oddPage);
					}
				} else if (page_no == total_page_no) {
					if (headers.lastPage) {
						displayHeaderFooter(headers, headers.lastPage);
					} else {
						if (total_page_no % 2 == 0) {
							displayHeaderFooter(headers, headers.evenPage);
						} else {
							displayHeaderFooter(headers, headers.oddPage);
						}
					}
					if (footers.lastPage) {
						displayHeaderFooter(footers, footers.lastPage);
					} else {
						if (total_page_no % 2 == 0) {
							displayHeaderFooter(footers, footers.evenPage);
						} else {
							displayHeaderFooter(footers, footers.oddPage);
						}
					}
				} else if (page_no % 2 == 0) {
					displayHeaderFooter(headers, headers.evenPage);
					displayHeaderFooter(footers, footers.evenPage);
				} else {
					displayHeaderFooter(headers, headers.oddPage);
					displayHeaderFooter(footers, footers.oddPage);
				}
		}
		</script>

		{% for tag in styles -%}
			{{ tag | string }}
		{%- endfor %}
		<link rel="preconnect" href="https://fonts.gstatic.com" />
		{% if html_id=="header-html" %}
		{% if headerFonts %}{{ headerFonts }}{%endif%}
		{% else %}
		{% if footerFonts %}{{ footerFonts }}{%endif%}
		{% endif %}
	</head>
	<body onload="subst()">
		{% for tag in content -%}
			{{ tag | string }}
		{%- endfor %}
		</div>
	</body>
</html>
