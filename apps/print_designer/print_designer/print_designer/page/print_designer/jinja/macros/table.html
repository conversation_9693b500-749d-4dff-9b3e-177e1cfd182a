{% macro table(element, send_to_jinja, heightType) -%}
    {%- set heightType = element.get("heightType") -%}
    {%- if settings.get("schema_version") == "1.1.0" -%}
        {%- set heightType = "auto" if element.get("isDynamicHeight", False) else "fixed" -%}
    {%- endif -%}
    <table style="position:{%- if heightType != 'fixed' -%}relative{% else %}absolute{%- endif -%}; {%- if heightType == 'fixed' -%}top: {%- else -%}margin-top: {%- endif -%}{{ element.startY }}px; left:{{ element.startX }}px; width:{{ element.width }}px;{%- if heightType != 'fixed' and heightType != 'auto' -%}{%- if heightType == 'auto-min-height' -%}min-{%- endif -%}height:{{ element.height }}px;{%- endif -%} max-width:{{ element.width }}px;" class="table-container printTable {{ element.classes | join(' ') }}">
            <thead>
            {% if element.columns %}
                <tr>
            {% for column in element.columns%}
                    <th style="{% if column.width %}width: {{column.width}}%; max-width: {{column.width}}%;{%endif%} {{convert_css(element.headerStyle)}}border-top-style: solid !important;border-bottom-style: solid !important;{%if loop.first%}border-left-style: solid !important;{%elif loop.last%}border-right-style: solid !important;{%endif%}{%- if column.applyStyleToHeader and column.style -%}{{convert_css(column.style)}}{%- endif -%}">
                    {{ _(column.label) }}
                    </th>
            {% endfor %}
                </tr>
            {% endif %}
            </thead>
            <tbody>
            {% if element.columns %}
            {% for row in doc.get(element.table.fieldname)%}
                <tr>
                    {% set isLastRow = loop.last %}
                {% for column in element.columns%}
                    <td style="{{convert_css(element.style)}}{%if row.idx % 2 == 0 %}{{convert_css(element.altStyle)}}{%endif%}{%if isLastRow%}border-bottom-style: solid !important;{%endif%}{%if loop.first%}border-left-style: solid !important;{%elif loop.last%}border-right-style: solid !important;{%endif%}{%- if column.style -%}{{convert_css(column.style)}}{%- endif -%}">
                {% if column is mapping %}
                    {% for field in column.dynamicContent%}
                        {{ span_tag(field, element, row, send_to_jinja) }}
                    {% endfor %}
            {% endif %}
                    </td>
                {% endfor %}
                </tr>
                {% endfor %}
            {% endif %}
            </tbody>
    </table>
{%- endmacro %}