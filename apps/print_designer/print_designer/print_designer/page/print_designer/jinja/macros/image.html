{% macro image(element) -%}
{%- if element.image.file_url -%}
    {%- set value = element.image.file_url -%}
{%- elif element.image.fieldname -%}
    {%- if element.image.parent == doc.doctype -%}
    {%- set value = doc.get(element.image.fieldname) -%}
    {%- else -%}
    {%- set value = frappe.db.get_value(element.image.doctype, doc[element.image.parentField], element.image.fieldname) -%}
    {%- endif -%}
{%- else -%}
    {%- set value = "" -%}
{%- endif -%}

{%- if value -%}
<div
    style="position: absolute; top:{{ element.startY }}px; left:{{ element.startX }}px;width:{{ element.width }}px;height:{{ element.height }}px;
{{convert_css(element.style)}}"
    class="image {{ element.classes | join(' ') }}"
>
    <div
        style="width:100%; height:100%; background-image: url('{{frappe.get_url()}}{{value}}');"
        class="image {{ element.classes | join(' ') }}"
    ></div>
</div>
{%- endif -%}
{%- endmacro %}