// Vuetify setup for csf_tz
// This file properly initializes Vuetify for use in jobcards

// Import Vuetify CSS
import 'vuetify/dist/vuetify.min.css';

// Make sure Vue and Vuetify are available globally
if (typeof window !== 'undefined') {
    // Ensure Vue is available globally
    if (!window.Vue) {
        console.error('Vue is not available. Please ensure Vue is loaded before Vuetify.');
    }
    
    // Ensure Vuetify is available globally
    if (!window.Vuetify) {
        // Import Vuetify if not already available
        import('vuetify/dist/vuetify.js').then(() => {
            console.log('Vuetify loaded successfully');
        }).catch(err => {
            console.error('Failed to load Vuetify:', err);
        });
    }
}
