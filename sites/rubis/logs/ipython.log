2025-06-19 17:42:19,491 INFO ipython === bench console session ===
2025-06-19 17:42:19,493 INFO ipython import frappe
2025-06-19 17:42:19,493 INFO ipython # Create a test employee
2025-06-19 17:42:19,493 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active"
                                })
2025-06-19 17:42:19,493 INFO ipython test_employee.insert()
2025-06-19 17:42:19,494 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,494 INFO ipython # Test deletion immediately
2025-06-19 17:42:19,494 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,494 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,494 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,494 INFO ipython # Get a department first
2025-06-19 17:42:19,494 INFO ipython departments = frappe.get_all("Department", limit=1)
2025-06-19 17:42:19,495 INFO ipython if departments:
        dept = departments[0].name
        else:
                # Create a test department
2025-06-19 17:42:19,495 INFO ipython     test_dept = frappe.get_doc({
        "doctype": "Department",
            "department_name": "Test Department"
            })
2025-06-19 17:42:19,495 INFO ipython     test_dept.insert()
2025-06-19 17:42:19,495 INFO ipython     dept = test_dept.name
2025-06-19 17:42:19,495 INFO ipython print(f"Using department: {dept}")
2025-06-19 17:42:19,495 INFO ipython # Create a test employee with all required fields
2025-06-19 17:42:19,495 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee", 
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": dept,
                                        "national_identity": "123456789"
                                        })
2025-06-19 17:42:19,495 INFO ipython test_employee.insert()
2025-06-19 17:42:19,496 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,496 INFO ipython # Test deletion
2025-06-19 17:42:19,496 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,496 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,497 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,497 INFO ipython frappe.delete_doc("Employee", "HR-EMP-00028")
2025-06-19 17:42:19,497 INFO ipython print("✅ SUCCESS: Employee HR-EMP-00028 deleted successfully!")
2025-06-19 17:42:19,497 INFO ipython === session end ===
2025-06-19 17:50:25,977 INFO ipython === bench console session ===
2025-06-19 17:50:25,978 INFO ipython import frappe
2025-06-19 17:50:25,978 INFO ipython # Create a test employee
2025-06-19 17:50:25,978 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Final",
            "first_name": "Test",
                "last_name": "Final",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "987654321"
                                        })
2025-06-19 17:50:25,978 INFO ipython test_employee.insert()
2025-06-19 17:50:25,978 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:50:25,978 INFO ipython # Test deletion
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 17:50:25,979 INFO ipython print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:50:25,979 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,979 INFO ipython # Test force deletion (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython test_employee2 = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Force",
            "first_name": "Test",
                "last_name": "Force",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "111222333"
                                        })
2025-06-19 17:50:25,979 INFO ipython test_employee2.insert()
2025-06-19 17:50:25,979 INFO ipython print(f"Created test employee: {test_employee2.name} - {test_employee2.employee_name}")
2025-06-19 17:50:25,979 INFO ipython # Force delete (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee2.name, force=True)
2025-06-19 17:50:25,980 INFO ipython print(f"✅ SUCCESS: Employee {test_employee2.name} force deleted successfully!")
2025-06-19 17:50:25,980 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,980 INFO ipython === session end ===
2025-06-19 18:02:39,571 INFO ipython === bench console session ===
2025-06-19 18:02:39,572 INFO ipython import frappe
2025-06-19 18:02:39,572 INFO ipython # Create a final test employee
2025-06-19 18:02:39,572 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Final Test Employee",
            "first_name": "Final",
                "last_name": "Test",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "999888777"
                                        })
2025-06-19 18:02:39,572 INFO ipython test_employee.insert()
2025-06-19 18:02:39,573 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 18:02:39,573 INFO ipython # Test normal deletion (should work without errors now)
2025-06-19 18:02:39,573 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 18:02:39,573 INFO ipython print(f"🎉 SUCCESS: Employee {test_employee.name} deleted successfully WITHOUT ERRORS!")
2025-06-19 18:02:39,573 INFO ipython frappe.db.commit()
2025-06-19 18:02:39,573 INFO ipython === session end ===
