#!/usr/bin/env python3

import frappe

def main():
    print("=== Employee Deletion Diagnostic Script ===")
    
    # Check if the Employee record exists
    print("\n1. Checking Employee Record...")
    try:
        emp = frappe.get_doc("Employee", "HR-EMP-00025")
        print(f"✓ Employee found: {emp.employee_name}")
    except Exception as e:
        print(f"✗ Error getting employee: {e}")
        return
    
    # Find all doctypes with links to Employee
    print("\n2. Finding DocTypes with Employee Links...")
    try:
        links = frappe.get_all("DocField", 
            filters={"options": "Employee", "fieldtype": "Link"}, 
            fields=["parent", "fieldname", "label"])
        
        print(f"Found {len(links)} doctypes linking to Employee")
        
        # Show first 10
        for i, link in enumerate(links[:10]):
            print(f"  {i+1}. {link.parent} -> {link.fieldname} ({link.label})")
            
    except Exception as e:
        print(f"✗ Error finding links: {e}")
    
    # Check for problematic child tables
    print("\n3. Checking for problematic child tables...")
    problematic_doctypes = []
    
    # Get all child table doctypes that might be problematic
    child_doctypes = [
        "Employee Group Table", 
        "Supplier Scorecard Standing",
        "Supplier Scorecard Scoring Standing"
    ]
    
    for dt in child_doctypes:
        try:
            meta = frappe.get_meta(dt)
            has_parent = any(field.fieldname == 'parent' for field in meta.fields)
            print(f"  {dt}: Has parent field = {has_parent}")
            if not has_parent:
                problematic_doctypes.append(dt)
        except Exception as e:
            print(f"  ✗ Error checking {dt}: {e}")
            problematic_doctypes.append(dt)
    
    print(f"\nProblematic doctypes: {problematic_doctypes}")
    
    # Try to identify which doctype is causing the deletion issue
    print("\n4. Testing deletion check...")
    try:
        # This will simulate what happens during deletion
        from frappe.model.delete_doc import check_if_doc_is_linked
        doc = frappe.get_doc("Employee", "HR-EMP-00025")
        check_if_doc_is_linked(doc)
        print("✓ No linking issues found")
    except Exception as e:
        print(f"✗ Linking check failed: {e}")
        
        # Try to identify the specific problematic table
        error_msg = str(e)
        if "Unknown column 'parent'" in error_msg:
            print("  Issue: A table is missing the 'parent' column")
            print("  This suggests a child table doctype has incorrect structure")

if __name__ == "__main__":
    main()
