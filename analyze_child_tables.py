#!/usr/bin/env python3

import os
import json
import glob

def analyze_child_tables():
    """Analyze all child table doctypes in csf_tz app to find missing parent fields"""
    
    print("=== Analyzing CSF TZ Child Tables ===")
    
    # Find all JSON files in csf_tz app
    json_files = glob.glob("apps/csf_tz/**/*.json", recursive=True)
    
    child_tables = []
    problematic_tables = []
    
    for json_file in json_files:
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                
            # Check if it's a child table
            if data.get('istable') == 1:
                doctype_name = data.get('name', 'Unknown')
                fields = data.get('fields', [])
                field_names = [field.get('fieldname') for field in fields]
                
                # Check for required parent fields
                has_parent = 'parent' in field_names
                has_parenttype = 'parenttype' in field_names
                has_parentfield = 'parentfield' in field_names
                
                child_table_info = {
                    'name': doctype_name,
                    'file': json_file,
                    'has_parent': has_parent,
                    'has_parenttype': has_parenttype,
                    'has_parentfield': has_parentfield,
                    'missing_fields': []
                }
                
                # Identify missing fields
                if not has_parent:
                    child_table_info['missing_fields'].append('parent')
                if not has_parenttype:
                    child_table_info['missing_fields'].append('parenttype')
                if not has_parentfield:
                    child_table_info['missing_fields'].append('parentfield')
                
                child_tables.append(child_table_info)
                
                # If any parent fields are missing, it's problematic
                if child_table_info['missing_fields']:
                    problematic_tables.append(child_table_info)
                    
        except Exception as e:
            print(f"Error processing {json_file}: {e}")
    
    # Print summary
    print(f"\nFound {len(child_tables)} child tables in csf_tz app")
    print(f"Found {len(problematic_tables)} problematic child tables")
    
    print("\n=== Problematic Child Tables ===")
    for table in problematic_tables:
        missing = ', '.join(table['missing_fields'])
        print(f"❌ {table['name']} - Missing: {missing}")
    
    print("\n=== Good Child Tables ===")
    good_tables = [t for t in child_tables if not t['missing_fields']]
    for table in good_tables:
        print(f"✅ {table['name']} - Has all parent fields")
    
    # Save results to file for the fix script
    with open('child_tables_analysis.json', 'w') as f:
        json.dump({
            'all_child_tables': child_tables,
            'problematic_tables': problematic_tables,
            'good_tables': good_tables
        }, f, indent=2)
    
    print(f"\nAnalysis saved to child_tables_analysis.json")
    print(f"Ready to fix {len(problematic_tables)} problematic child tables")
    
    return problematic_tables

if __name__ == "__main__":
    analyze_child_tables()
